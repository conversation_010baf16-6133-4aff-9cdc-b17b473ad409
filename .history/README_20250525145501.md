
# TriGo Lite

This is a Next.js application for TriGo Dispatch Lite, a real-time trider monitoring and dispatching system. Built with Next.js, TypeScript, Tailwind CSS, ShadCN UI components, and Mapbox GL JS.

## Features

- **Homepage (`/`):**
  - Dynamic, glassmorphic landing page with an animated background.
  - Central triangular TriGo logo (auto-rickshaw SVG) that flips and has an electric aura effect on hover, linking to `https://trigo.live`.
  - Role selection cards (Passenger, Trider, Dispatcher, Admin) with unique icons and color themes, linking to respective demo/dashboard pages.
- **Dispatch Dashboard (`/dispatcher`):**
  - Live map visualization of triders and ride requests using Mapbox.
  - TODA (Tricycle Operators and Drivers' Association) zone boundaries displayed on the map.
  - Real-time (mocked) updates for trider locations and incoming ride requests.
  - Selection of triders and ride requests for dispatch; triders are dynamically filtered based on the selected ride's TODA zone.
  - Route calculation (shortest distance among alternatives) and ETA preview using Mapbox Directions API.
  - Manual dispatch functionality (triders can only serve pickups within their assigned TODA zone).
  - Heatmap overlay for ride request density.
  - AI-driven insights and alerts (currently mocked).
  - **View Switcher:** Toggle between "Dispatch Control" (lists, forms) and "Map View".
- **Trider Management (`/dispatcher/triders`):**
  - Comprehensive list of all triders with details: name, body number, TODA zone, vehicle type, status.
  - Filtering options: by name, TODA zone, status.
  - Sorting functionality by name, body number, TODA zone, or status.
  - Detailed Trider panel:
    - Live GPS preview on a mini-map.
    - Status controls (force online/offline, ping trider, suspend/unsuspend).
    - Wallet information: balance, earnings, payout history, recent rides (mocked).
    - Simulated payout functionality.
    - TODA Zone change request approval/rejection.
  - Dispatcher-to-Trider chat functionality (mocked, UI in place).
- **Passenger Role Simulation (`/passenger`):**
  - **New Landing Page:** Initial view for passengers featuring "Ride Before", "Ride Now", "Ride Later" options, with a background image and themed elements.
  - **Fully Functional Button Interface:**
    - All landing page buttons are now functional with appropriate actions and feedback.
    - Menu, Search, Map Style Toggle, and Login/Logout buttons in header provide interactive functionality.
    - Enhanced "Ride Before" and "Ride Later" buttons with detailed feature descriptions.
    - Map style cycling (Streets → Satellite → Dark) with real-time preview and toast notifications.
  - **Navigation System:**
    - Bottom navigation bar with functional links to Home, Map View, and Profile pages.
    - Dedicated Map page (`/passenger/map`) with enhanced map features and controls.
    - Comprehensive Profile page (`/passenger/profile`) with user management capabilities.
  - **Payment Methods Integration:**
    - Complete payment methods management in profile page.
    - Support for GCash (with actual logo), PayMaya (with actual logo), and TriCoin (custom gold icon).
    - Set default payment method functionality with visual feedback.
    - Balance display and payment method status indicators.
    - Modal-based payment method selection and management.
  - **Requesting Ride:**
    - Map view to select pickup and dropoff locations.
    - Initial pickup suggestion via geolocation. "Locate Me" button inside pickup input field.
    - Address input with autocomplete/suggestions using Mapbox Geocoding API.
  - **Ride In Progress:**
    - Simulation of trider assignment, movement to pickup (following Mapbox route), and trip to destination (following Mapbox route).
    - Displays trider's live location and ETA on the map, with distinct route colors (trider-to-pickup: accent green, pickup-to-dropoff: passenger theme orange).
    - Orange-themed header, white page background.
    - Glassmorphism countdown timer card with neon green text for ETA, visual cues for final 10 seconds. Timer is recomputed periodically for accuracy.
    - Ride Ticket ID display.
    - Ride receipt dialog displayed upon ride completion (data logged to console, not DB).
  - **"Pick Me Up Now" Feature (from `confirmingRide` state):**
    - A prominent button allows passengers to quickly request a ride from their current location to their selected destination.
    - The system automatically identifies the nearest available (online) trider within the passenger's TODA zone (mocked).
    - If the nearest trider does not accept within 10 seconds, the request is automatically forwarded to the next nearest available trider (simulated by assigning a random trider).
  - Customizable map style (streets, satellite, dark) per passenger, saved in `localStorage`.
- **Trider Role Simulation (`/trider`):**
  - **Bottom Navigation:** Dashboard, Wallet, Settings, Premium views.

  - **Dashboard:** Manage status (online/offline). Geolocation on going online. View and accept incoming ride requests within their TODA zone. Map view showing current location, active ride details, and route. Simulation of movement following Mapbox routes.
  - **Wallet (Mocked):** View balance, send/add mock "TriCoin", view mock transaction history.
  - **Settings (Mocked):** Toggle notification preferences, select map style (saved to localStorage).

  - **Premium (Mocked):** View subscription status, mock upgrade/downgrade.
  - Ability to request a change to a different TODA zone.
- **Application Settings (`/dispatcher/settings`):**
  - Customizable theme (light, dark, system).
  - Configuration for default map zoom and center coordinates.
  - Toggle for heatmap visibility on the dispatch map.
  - Adjustable intervals for mock data simulation (new rides, trider updates, AI insights).
  - Configuration for global convenience fee (PIN-protected for demo) and per-TODA base fares.
- **TODA Zones Management (`/dispatcher/toda-management`):**
  - Dedicated page to configure the fare matrix.
  - Set global default base fare and per KM charge.
  - Modal-based editing to override base fares and set Terminal Exit Points (coordinates & address) for specific TODA zones.
  - Search functionality for TODA zones.
  - Placeholder sections for future TODA/Trider/Passenger CRUD operations.
- **Admin Dashboard (`/dispatcher/admin-dashboard`):**
  - Modern UI with real-time (mocked) metrics: Total Users, Triders, Weekly Revenue, Completed Rides with animated counters.
  - Platform Activity line chart (simulated active sessions).
  - Performance metrics panel with progress bars and update effects.
  - Live scrolling notification feed for recent system activities.
  - Quick access buttons grid with glassmorphism style and hover effects.
- **Authentication:** (Currently Removed)
  - Sign-in (`/sign-in`) page features a Role Switcher and Passenger Profile Selector.
  - Sign-up (`/sign-up`) page is a placeholder.

## Getting Started

### Prerequisites

- Node.js (LTS version recommended)
- npm or yarn
- A Mapbox Access Token

### Installation

1.  **Clone the repository:**
    ```bash
    git clone https://github.com/dagz55/trigo-lite.git
    cd trigo-lite
    ```

2.  **Install dependencies:**
    ```bash
    npm install
    # or
    yarn install
    ```

3.  **Set up environment variables:**
    Create a `.env.local` file in the root of your project and add the following:

    ```env
    # Mapbox Configuration
    NEXT_PUBLIC_MAPBOX_ACCESS_TOKEN=your_mapbox_access_token_here

    # Genkit/AI Configuration (if used)
    # Example for Google AI Studio API Key
    # GOOGLE_API_KEY=your_google_ai_studio_api_key
    ```

4.  **Run the development server:**
    ```bash
    npm run dev
    # or
    yarn dev
    ```
    The application will be available at `http://localhost:9002` by default.

5.  **(Optional) Run Genkit development server (if using AI features):**
    In a separate terminal:
    ```bash
    npm run genkit:dev
    # or for watching changes
    npm run genkit:watch
    ```

## Tech Stack

- **Framework:** Next.js (App Router)
- **Language:** TypeScript
- **Styling:** Tailwind CSS, ShadCN UI
- **Mapping:** Mapbox GL JS, react-map-gl
- **State Management:** React Context (for settings), React Hooks (for page-level state)
- **Charts:** Recharts
- **Linting/Formatting:** ESLint, Prettier (via Next.js defaults)
- **AI (Optional):** Genkit

## Project Structure (Key Directories)

```
.
├── src/
│   ├── app/                        # Next.js App Router (pages and layouts)
│   │   ├── dispatcher/             # Dispatcher-specific routes
│   │   │   ├── layout.tsx          # Layout for dispatcher section (sidebar, header)
│   │   │   ├── page.tsx            # Main dispatch dashboard page
│   │   │   ├── triders/            # Trider management section
│   │   │   ├── settings/           # Application settings page
│   │   │   ├── toda-management/    # TODA Zone and fare management page
│   │   │   └── admin-dashboard/    # Admin overview dashboard
│   │   ├── passenger/              # Passenger role simulation
│   │   ├── trider/                 # Trider role simulation
│   │   ├── (sign-in|sign-up)/      # Placeholder auth pages with Role Switcher
│   │   ├── globals.css             # Global styles and Tailwind directives
│   │   └── layout.tsx              # Root layout
│   ├── components/                 # UI components
│   │   ├── dispatch/               # Components specific to the dispatch dashboard
│   │   ├── map/                    # Map-related components
│   │   ├── passenger/              # Components specific to the passenger demo
│   │   ├── triders/                # Components for the trider management dashboard
│   │   ├── ui/                     # ShadCN UI components
│   │   └── RoleSwitcher.tsx        # Component for role selection on sign-in
│   ├── contexts/                   # React Contexts (e.g., SettingsContext)
│   ├── data/                       # Static data (TODA zones, mock passenger profiles)
│   ├── hooks/                      # Custom React hooks (use-mobile, use-toast)
│   ├── lib/                        # Utility functions (geoUtils, utils)
│   ├── middleware.ts               # Next.js middleware (minimal)
│   └── types/                      # TypeScript type definitions
├── public/                         # Static assets
├── .env.local                      # Environment variables
├── next.config.ts                  # Next.js configuration
└── ...
```

## Available Scripts

- `npm run dev`: Starts the Next.js development server (Turbopack enabled on port 9002).
- `npm run genkit:dev`: Starts the Genkit development server.
- `npm run genkit:watch`: Starts the Genkit development server with file watching.
- `npm run build`: Builds the application for production.
- `npm run start`: Starts the production server.
- `npm run lint`: Lints the codebase.
- `npm run typecheck`: Runs TypeScript type checking.

## Contributing

Please refer to contributing guidelines if available. For now, ensure code quality, follow existing patterns, and write clear commit messages.

## License

This project is licensed under [Specify License Here - e.g., MIT License].
