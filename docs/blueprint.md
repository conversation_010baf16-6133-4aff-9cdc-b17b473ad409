# **App Name**: TriGo Dispatch Lite

## Core Features:

- Map Visualization: Display a map interface using Mapbox GL JS to visualize tricycles (triders) and passenger locations in real-time.
- Heatmap Overlay: Implement a ride request heatmap overlay on the map to identify high-demand areas, assisting in dispatching.
- Trider Monitoring: Show real-time trider locations. An AI tool will use historic locations to identify possible popular routes that are unavailable due to triders having other active requests. The AI tool will notify the dispatcher when routes are seeing higher than usual demand.
- Manual Dispatching: Implement a basic dispatching feature to manually assign ride requests to available triders, improving response times.
- Route Preview: Use Mapbox Directions API to calculate and display routes and ETAs for dispatched rides.

## Style Guidelines:

- Primary color: Teal (#008080) to reflect trust.
- Secondary color: Light gray (#F0F0F0) for backgrounds.
- Accent: Lime green (#32CD32) for active elements and confirmations.
- Clean and modern typography.
- Simple and clear icons representing trider and passenger actions.
- Clean layout.