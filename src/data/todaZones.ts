import type { TodaZone } from '@/types';

export const todaZones: TodaZone[] = [
  { id: '1', name: 'ACAPODA', areaOfOperation: 'Admiral Village, Talon Tres', center: { latitude: 14.440300, longitude: 121.000600 }, radiusKm: 0.5, boundary: [] },
  { id: '2', name: 'APHDA', areaOfOperation: 'Angela Village, Talon Kuatro', center: { latitude: 14.436900, longitude: 120.996900 }, radiusKm: 0.5, boundary: [] },
  { id: '3', name: 'ATODA', areaOfOperation: 'Pilar Village', center: { latitude: 14.416700, longitude: 121.008200 }, radiusKm: 1.0, boundary: [] },
  { id: '4', name: '<PERSON><PERSON><PERSON>OD<PERSON>', areaOfOperation: 'BF Executive, Almanza Uno', center: { latitude: 14.430000, longitude: 121.015000 }, radiusKm: 0.5, boundary: [] },
  { id: '5', name: 'BFRSSC<PERSON>', areaOfOperation: 'BF Sta. Cecilia Village, Talon Dos', center: { latitude: 14.432500, longitude: 121.005000 }, radiusKm: 0.5, boundary: [] },
  { id: '6', name: 'BFRV-VG', areaOfOperation: 'BF Vista Grande, Talon Dos', center: { latitude: 14.435000, longitude: 121.010000 }, radiusKm: 0.3, boundary: [] },
  { id: '7', name: 'TEPTODA', areaOfOperation: 'Talon Equitable (Pulong)', center: { latitude: 14.435000, longitude: 121.000000 }, radiusKm: 0.3, boundary: [] },
  { id: '8', name: 'GGTODA', areaOfOperation: 'Golden Gate Subdivision, Talon Tres', center: { latitude: 14.442000, longitude: 120.995000 }, radiusKm: 0.4, boundary: [] },
  { id: '9', name: 'BFATODA', areaOfOperation: 'BF Almanza (BF Homes Almanza), Almanza Dos', center: { latitude: 14.428000, longitude: 121.027000 }, radiusKm: 0.7, boundary: [] },
  { id: '10', name: 'MAMTTODA', areaOfOperation: 'Moonwalk Village, Talon Singko', center: { latitude: 14.403000, longitude: 121.012000 }, radiusKm: 0.5, boundary: [] },
  { id: '11', name: 'MDVPTODA', areaOfOperation: 'Manila Doctors Village, Almanza Uno', center: { latitude: 14.442000, longitude: 120.983000 }, radiusKm: 0.4, boundary: [] },
  { id: '12', name: 'MSTODA', areaOfOperation: 'Metrocor Subdivision, Almanza Uno', center: { latitude: 14.441000, longitude: 120.978000 }, radiusKm: 0.2, boundary: [] },
  { id: '13', name: 'PVTODA', areaOfOperation: 'Philamlife Village, Pamplona Dos', center: { latitude: 14.447800, longitude: 120.977100 }, radiusKm: 0.6, boundary: [] },
  { id: '14', name: 'RSTODA', areaOfOperation: 'Remarville Subdivision, Pamplona Dos', center: { latitude: 14.447000, longitude: 120.979000 }, radiusKm: 0.3, boundary: [] },
  { id: '15', name: 'SAVTODA', areaOfOperation: 'SAV 17 (Soldiers Housing Area), Talon Kuatro', center: { latitude: 14.434000, longitude: 120.990000 }, radiusKm: 0.2, boundary: [] },
  { id: '16', name: 'SMCTODA', areaOfOperation: 'Saint Michael Village, Talon Dos', center: { latitude: 14.430000, longitude: 121.001000 }, radiusKm: 0.3, boundary: [] },
  { id: '17', name: 'TSCTODA', areaOfOperation: 'T.S. Cruz Subdivision, Almanza Dos', center: { latitude: 14.421700, longitude: 121.018100 }, radiusKm: 0.8, boundary: [] },
  { id: '18', name: 'TSTODA', areaOfOperation: 'Talon Singko (Barangay Talon 5 area)', center: { latitude: 14.405000, longitude: 121.000000 }, radiusKm: 1.5, boundary: [] },
  { id: '19', name: 'NUVTODA', areaOfOperation: 'Urbanville Village, Talon Tres', center: { latitude: 14.439000, longitude: 120.998000 }, radiusKm: 0.2, boundary: [] },
  { id: '20', name: 'ZOLIVIMATODA', areaOfOperation: 'DBP Village, Almanza Uno', center: { latitude: 14.430000, longitude: 121.000000 }, radiusKm: 0.4, boundary: [] },
  { id: '21', name: 'P1TODA', areaOfOperation: 'Pamplona Uno', center: { latitude: 14.4550, longitude: 120.9800 }, radiusKm: 0.7, boundary: [] },
];
